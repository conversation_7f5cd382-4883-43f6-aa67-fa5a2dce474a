import React from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Text,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../theme';

interface SearchBarWithBackProps {
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
  showBackButton?: boolean;
  onBackPress?: () => void;
}

export default function SearchBarWithBack({
  placeholder,
  value,
  onChangeText,
  showBackButton = false,
  onBackPress
}: SearchBarWithBackProps) {
  const navigation = useNavigation();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.container}>
      {showBackButton && (
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
      )}
      <TextInput
        style={[styles.searchBar, showBackButton && styles.searchBarWithBack]}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        placeholderTextColor={getColor('neutral', 500)}
        textAlign="right"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing('lg'),
  },
  backButton: {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('sm'),
    marginRight: getSpacing('sm'),
    ...getShadow('sm'),
    minWidth: 40,
    alignItems: 'center',
  },
  backButtonText: {
    ...getTypography('lg', 'bold'),
    color: getColor('primary'),
  },
  searchBar: {
    flex: 1,
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('full'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('md'),
    ...getTypography('base'),
    ...getShadow('sm'),
  },
  searchBarWithBack: {
    // Additional styles when back button is present
  },
});
