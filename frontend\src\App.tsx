import AsyncStorage from '@react-native-async-storage/async-storage';
import { NavigationContainer, useNavigationContainerRef } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import React, { useMemo, useEffect, useRef, useState } from 'react';
import { useColorScheme, Platform } from 'react-native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { ErrorBoundary } from '@components';
import { AuthProvider, useAuth, ThemeProvider, useThemePreferences, getEffectiveTheme } from '@contexts';
import AppNavigator from '@navigation/AppNavigator';
import { createTheme } from '@theme/variants';
import '@i18n/index';

// Test if basic React Native components work
console.log('App.tsx loaded successfully');

// Navigation state persistence key
const NAVIGATION_PERSISTENCE_KEY = 'NAVIGATION_STATE_V1';

// Inner app component that uses theme context
const AppContent: React.FC = () => {
  const { preferences, isLoading } = useThemePreferences();
  const { user } = useAuth();
  const systemColorScheme = useColorScheme();
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const navigationRef = useNavigationContainerRef();
  const [savedNavigationState, setSavedNavigationState] = useState<any>(null);

  const isDark = getEffectiveTheme(preferences, systemColorScheme) === 'dark';

  // Memoize theme creation to prevent unnecessary re-creations
  const theme = useMemo(() => {
    return createTheme(preferences, isDark);
  }, [preferences, isDark]);

  // Load navigation state on app start
  useEffect(() => {
    const restoreState = async () => {
      try {
        const savedStateString = await AsyncStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
        const state = savedStateString ? JSON.parse(savedStateString) : undefined;
        if (state !== undefined) {
          console.log('🔄 Restoring navigation state');
          setSavedNavigationState(state);
        }
      } catch (e) {
        console.warn('Failed to restore navigation state:', e);
      } finally {
        setIsNavigationReady(true);
      }
    };

    if (!isNavigationReady) {
      restoreState();
    }
  }, [isNavigationReady]);

  if (isLoading || !isNavigationReady) {
    return null;
  }

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer
        ref={navigationRef}
        onReady={() => {
          setIsNavigationReady(true);
        }}
        onStateChange={(state) => {
          if (state) {
            setSavedNavigationState(state);
            AsyncStorage.setItem(NAVIGATION_PERSISTENCE_KEY, JSON.stringify(state));
          }
        }}
      >
        <ErrorBoundary>
          <AppNavigator />
        </ErrorBoundary>
      </NavigationContainer>
      <StatusBar style={isDark ? "light" : "dark"} />
    </PaperProvider>
  );
};

const App: React.FC = () => {
  console.log('🎉 Full Kibbutz App Loading...');

  if (typeof document !== 'undefined') {
    document.title = 'Kibbutz App';
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <AuthProvider>
          <ThemeProvider>
            <AppContent />
          </ThemeProvider>
        </AuthProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
};

export default App;
