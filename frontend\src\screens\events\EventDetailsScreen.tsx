import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRoute, RouteProp } from '@react-navigation/native';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';
import WebCompatibleScrollView from '../../components/WebCompatibleScrollView';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '../../utils/webStyles';
import { EventsStackParamList } from '../../navigation/types';
import { Event } from '../../types';
import { eventsService } from '../../services/events';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography, getColorWithOpacity } from '../../theme';

type EventDetailsScreenRouteProp = RouteProp<EventsStackParamList, 'EventDetails'>;

export default function EventDetailsScreen() {
  const { t } = useTranslation();
  const route = useRoute<EventDetailsScreenRouteProp>();
  const { eventId } = route.params;

  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadEvent();
  }, [eventId]);

  const loadEvent = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading event details for ID:', eventId);
      const eventData = await eventsService.getEventById(eventId);

      if (eventData) {
        setEvent(eventData);
        console.log('✅ Event loaded successfully:', eventData.title);
      } else {
        Alert.alert('שגיאה', 'האירוע לא נמצא');
      }
    } catch (error) {
      console.error('❌ Error loading event:', error);
      Alert.alert('שגיאה', 'שגיאה בטעינת פרטי האירוע');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('he-IL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Header
          title={t('events.eventDetails')}
          showBackButton={true}
        />
        <View style={styles.centerContainer}>
          <Text style={styles.loadingText}>טוען פרטי אירוע...</Text>
        </View>
      </View>
    );
  }

  if (!event) {
    return (
      <View style={styles.container}>
        <Header
          title={t('events.eventDetails')}
          showBackButton={true}
        />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>האירוע לא נמצא</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title={event.title}
        showBackButton={true}
      />
      <WebCompatibleScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* Event Hero Section */}
        <View style={styles.eventHero}>
          <View style={styles.eventDateLarge}>
            <Text style={styles.eventDayLarge}>{formatDate(event.date).split(' ')[0]}</Text>
            <Text style={styles.eventMonthLarge}>{formatDate(event.date).split(' ')[1]}</Text>
          </View>
          <Text style={styles.eventTitle}>{event.title}</Text>
          <Text style={styles.eventSubtitle}>
            יוצר: {event.createdBy} • {event.type === 'community' ? 'אירוע קהילתי' : 'אירוע דיירים'}
          </Text>
          {/* Display the selected icon prominently */}
          <View style={styles.eventIconDisplay}>
            <Text style={styles.eventIconLarge}>{event.icon || '📅'}</Text>
          </View>
        </View>

        {/* Event Details */}
        <View style={styles.eventDetails}>
          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>🕐</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>זמן ומשך</Text>
              <Text style={styles.detailDescription}>{formatDate(event.date)}</Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>📍</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>מיקום</Text>
              <Text style={styles.detailDescription}>{event.location || 'מיקום לא צוין'}</Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>👥</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>מי מגיע</Text>
              <Text style={styles.detailDescription}>יוצר: {event.createdBy}</Text>
            </View>
          </View>
        </View>

        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={[styles.sectionTitle, { textAlign: getTextAlign() }]}>
            תיאור האירוע
          </Text>
          <Text style={[styles.description, { textAlign: getTextAlign() }]}>
            {event.description}
          </Text>
        </View>

        {/* Action Button */}
        <TouchableOpacity style={styles.button}>
          <Text style={[styles.buttonText, { textAlign: getTextAlign() }]}>
            הצטרף לאירוע
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...createWebCompatibleContainerStyle(),
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  content: {
    padding: getSpacing('lg'),
  },
  scrollContent: {
    ...createWebCompatibleScrollContentStyle(),
    paddingBottom: 100,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getSpacing('lg'),
  },
  loadingText: {
    ...getTypography('base'),
    color: getColor('neutral', 500),
  },
  errorText: {
    ...getTypography('base'),
    color: getColor('error'),
  },
  eventHero: {
    backgroundColor: getColor('primary'),
    padding: getSpacing('xl'),
    alignItems: 'center',
    marginBottom: getSpacing('xxl'),
    borderRadius: getBorderRadius('3xl'),
    ...getShadow('lg'),
  },
  eventDateLarge: {
    backgroundColor: getColorWithOpacity('white', 500, 0.2),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    marginBottom: getSpacing('lg'),
  },
  eventDayLarge: {
    ...getTypography('5xl', 'bold'),
    color: getColor('white'),
    lineHeight: 36,
  },
  eventMonthLarge: {
    ...getTypography('sm', 'bold'),
    color: getColor('white'),
    textTransform: 'uppercase',
    marginTop: getSpacing('xs'),
  },
  eventTitle: {
    ...getTypography('2xl', 'bold'),
    color: getColor('white'),
    marginBottom: getSpacing('sm'),
    textAlign: 'center',
  },
  eventSubtitle: {
    ...getTypography('sm'),
    color: getColor('white'),
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: getSpacing('lg'),
  },
  eventIconDisplay: {
    backgroundColor: getColorWithOpacity('white', 500, 0.2),
    borderRadius: getBorderRadius('3xl'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  eventIconLarge: {
    ...getTypography('6xl'),
  },
  eventDetails: {
    padding: getSpacing('xxl'),
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    marginBottom: getSpacing('lg'),
    ...getShadow('sm'),
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing('lg'),
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: getBorderRadius('md'),
    backgroundColor: getColor('neutral', 100),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: getSpacing('lg'),
  },
  detailIconText: {
    ...getTypography('lg'),
  },
  detailText: {
    flex: 1,
  },
  detailTitle: {
    color: getColor('neutral', 800),
    ...getTypography('sm', 'semibold'),
    marginBottom: getSpacing('xs'),
    textAlign: 'right',
  },
  detailDescription: {
    color: getColor('neutral', 500),
    ...getTypography('xs'),
    lineHeight: 18,
    textAlign: 'right',
  },
  descriptionContainer: {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('xl'),
    ...getShadow('sm'),
  },
  sectionTitle: {
    ...getTypography('lg', 'bold'),
    color: getColor('neutral', 800),
    marginBottom: getSpacing('lg'),
  },
  description: {
    ...getTypography('base'),
    color: getColor('neutral', 600),
    lineHeight: 24,
  },
  button: {
    backgroundColor: getColor('primary'),
    paddingVertical: getSpacing('lg'),
    paddingHorizontal: getSpacing('xl'),
    borderRadius: getBorderRadius('full'),
    alignItems: 'center',
    marginTop: getSpacing('lg'),
    ...getShadow('sm'),
  },
  buttonText: {
    color: getColor('white'),
    ...getTypography('base', 'bold'),
  },
});
