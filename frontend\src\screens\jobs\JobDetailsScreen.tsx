import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import WebCompatibleScrollView from '../../components/WebCompatibleScrollView';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '../../utils/webStyles';

export default function JobDetailsScreen() {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <Header
        title="פרטי העבודה"
        showBackButton={true}
        icon="work"
        iconSet="MaterialIcons"
      />
      <WebCompatibleScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <Text style={[styles.title, { textAlign: getTextAlign() }]}>
          פרטי העבודה
        </Text>
        <Text style={[styles.subtitle, { textAlign: getTextAlign() }]}>
          פרטי העבודה יוצגו כאן
        </Text>

        <TouchableOpacity style={styles.button}>
          <Text style={[styles.buttonText, { textAlign: getTextAlign() }]}>הגש מועמדות</Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle(),
  content: {
    padding: getSpacing('lg'),
  },
  scrollContent: createWebCompatibleScrollContentStyle(),
  title: {
    ...getTypography('2xl', 'bold'),
    marginBottom: getSpacing('md'),
    color: getColor('neutral', 800),
  },
  subtitle: {
    ...getTypography('base'),
    color: getColor('neutral', 600),
    marginBottom: getSpacing('lg'),
  },
  button: {
    backgroundColor: getColor('info'),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    marginTop: getSpacing('lg'),
    ...getShadow('sm'),
  },
  buttonText: {
    color: getColor('white'),
    ...getTypography('base', 'bold'),
  },
});
