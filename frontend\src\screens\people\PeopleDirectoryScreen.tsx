import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput, Alert, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import Header from '../../components/Header';

interface User {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  phone?: string | null;
  user_type: string;
  user_role: string;
  is_active: boolean;
}

export default function PeopleDirectoryScreen() {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'A-Z' | 'Z-A'>('A-Z');

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [searchQuery, users, sortOrder]);

  const getInitials = (firstName: string | null, lastName: string | null) => {
    const firstInitial = firstName?.charAt(0) || '';
    const lastInitial = lastName?.charAt(0) || '';
    return (firstInitial + lastInitial).toUpperCase() || '?';
  };

  const getDisplayName = (firstName: string | null, lastName: string | null) => {
    if (!firstName && !lastName) return 'משתמש';
    if (!firstName) return lastName || 'משתמש';
    if (!lastName) return firstName;
    return `${firstName} ${lastName}`;
  };

  const getRoleText = (userRole: string) => {
    switch (userRole) {
      case 'ADMIN':
        return 'מנהל';
      case 'USER':
        return 'משתמש';
      default:
        return 'משתמש';
    }
  };

  const getStatusText = (user: User) => {
    // This is a placeholder - in a real app you might have last seen data
    const statuses = ['מחובר', 'לפני שעתיים', 'לפני יום', 'לפני 3 ימים'];
    return statuses[Math.floor(Math.random() * statuses.length)];
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      console.log('🔄 Fetching users...');
      
      const response = await apiService.getAllUsers();
      
      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        // Filter only active users
        const activeUsers = response.data.filter((user: User) => user.is_active);
        console.log('✅ Users fetched successfully:', activeUsers.length);
        setUsers(activeUsers);
      }
    } catch (error) {
      console.error('❌ Error fetching users:', error);
      Alert.alert(
        t('common.error'),
        t('people.fetchError') || 'לא ניתן לטעון את רשימת המשתמשים'
      );
    } finally {
      setLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users.filter(user => {
      const fullName = getDisplayName(user.first_name, user.last_name).toLowerCase();
      const email = user.email.toLowerCase();
      const query = searchQuery.toLowerCase();
      
      return fullName.includes(query) || email.includes(query);
    });

    // Sort users
    filtered.sort((a, b) => {
      const nameA = getDisplayName(a.first_name, a.last_name);
      const nameB = getDisplayName(b.first_name, b.last_name);
      
      if (sortOrder === 'A-Z') {
        return nameA.localeCompare(nameB, 'he');
      } else {
        return nameB.localeCompare(nameA, 'he');
      }
    });

    setFilteredUsers(filtered);
  };

  const handleContactUser = (user: User) => {
    const displayName = getDisplayName(user.first_name, user.last_name);
    
    if (user.phone) {
      Alert.alert(
        'פרטי קשר',
        `${displayName}\nטלפון: ${user.phone}\nאימייל: ${user.email}`,
        [
          { text: 'ביטול', style: 'cancel' },
          { text: 'התקשר', onPress: () => {/* Handle phone call */} },
          { text: 'שלח אימייל', onPress: () => {/* Handle email */} },
        ]
      );
    } else {
      Alert.alert(
        'פרטי קשר',
        `${displayName}\nאימייל: ${user.email}`,
        [
          { text: 'ביטול', style: 'cancel' },
          { text: 'שלח אימייל', onPress: () => {/* Handle email */} },
        ]
      );
    }
  };

  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'A-Z' ? 'Z-A' : 'A-Z');
  };

  const sortButton = (
    <TouchableOpacity style={styles.sortButton} onPress={toggleSortOrder}>
      <Text style={styles.sortButtonText}>{sortOrder}</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>
          טוען רשימת משתמשים...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="מדריך המשתמשים"
        showBackButton={true}
        rightComponent={sortButton}
        icon="people"
        iconSet="MaterialIcons"
      />

      {/* Content */}
      <View style={styles.content}>
        <TextInput
          style={styles.searchBar}
          placeholder="🔍 חפש משתמשים..."
          placeholderTextColor="#7f8c8d"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          {filteredUsers.map((user) => (
            <TouchableOpacity
              key={user.id}
              style={styles.userCard}
              onPress={() => handleContactUser(user)}
            >
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {getInitials(user.first_name, user.last_name)}
                </Text>
              </View>
              <View style={styles.userInfo}>
                <Text style={styles.userName}>
                  {getDisplayName(user.first_name, user.last_name)}
                </Text>
                <Text style={styles.userDetails}>
                  {getRoleText(user.user_role)} • {user.email}
                </Text>
              </View>
              <Text style={styles.statusText}>
                {getStatusText(user)}
              </Text>
            </TouchableOpacity>
          ))}

          {filteredUsers.length === 0 && (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                {searchQuery ? 'לא נמצאו משתמשים התואמים לחיפוש' : 'אין משתמשים רשומים'}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f6fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#2c3e50',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchBar: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  userCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userDetails: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  statusText: {
    fontSize: 12,
    color: '#27ae60',
    marginLeft: 8,
  },
  sortButton: {
    padding: 8,
  },
  sortButtonText: {
    fontSize: 16,
    color: '#3498db',
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
  },
});


