import { apiService } from './api';

export interface ChatGroup {
  id: string;
  name: string;
  createdAt: Date;
  createdBy: string;
  members: string[];
  iconUrl: string | null;
  dataAiHint: string | null;
  lastMessage: string | null;
  lastMessageTimestamp: Date | null;
}

export interface ChatMessage {
  id: string;
  groupId: string;
  content: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
  metadata?: any;
}

class ChatService {
  async getChatGroups(): Promise<ChatGroup[]> {
    try {
      const response = await apiService.getChatGroups();
      if (response.error) {
        throw new Error(response.error);
      }
      if (!response.data) {
        return [];
      }
      return response.data.map((group: any) => ({
        ...group,
        createdAt: new Date(group.createdAt),
        lastMessageTimestamp: group.lastMessageTimestamp ? new Date(group.lastMessageTimestamp) : null,
      }));
    } catch (error) {
      console.error('Error fetching chat groups:', error);
      throw error;
    }
  }

  async getChatGroup(groupId: string): Promise<ChatGroup> {
    try {
      const response = await apiService.getChatGroup(groupId);
      if (response.error) {
        throw new Error(response.error);
      }
      if (!response.data) {
        throw new Error('Chat group not found');
      }
      const group = response.data;
      return {
        ...group,
        createdAt: new Date(group.createdAt),
        lastMessageTimestamp: group.lastMessageTimestamp ? new Date(group.lastMessageTimestamp) : null,
      };
    } catch (error) {
      console.error('Error fetching chat group:', error);
      throw error;
    }
  }

  async getChatMessages(groupId: string): Promise<ChatMessage[]> {
    try {
      const response = await apiService.getChatMessages(groupId);
      if (response.error) {
        throw new Error(response.error);
      }
      if (!response.data) {
        return [];
      }
      return response.data.map((message: any) => ({
        ...message,
        timestamp: new Date(message.timestamp),
      }));
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      throw error;
    }
  }

  async sendMessage(groupId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>): Promise<ChatMessage> {
    try {
      const response = await apiService.sendMessage(groupId, {
        ...message,
        timestamp: new Date(),
      });
      if (response.error) {
        throw new Error(response.error);
      }
      if (!response.data) {
        throw new Error('Failed to send message');
      }
      return {
        ...response.data,
        timestamp: new Date(response.data.timestamp),
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async createChatGroup(groupData: Omit<ChatGroup, 'id' | 'createdAt'>): Promise<ChatGroup> {
    try {
      const response = await apiService.createChatGroup(groupData);
      if (response.error) {
        throw new Error(response.error);
      }
      if (!response.data) {
        throw new Error('Failed to create chat group');
      }
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        lastMessageTimestamp: response.data.lastMessageTimestamp ? new Date(response.data.lastMessageTimestamp) : null,
      };
    } catch (error) {
      console.error('Error creating chat group:', error);
      throw error;
    }
  }
}

export const chatService = new ChatService();
