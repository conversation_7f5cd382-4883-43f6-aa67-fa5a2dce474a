import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { createHeaderButtonStyle, getShadow, getSpacing, getTypography, getColor } from '../theme';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  rightComponent?: React.ReactNode;
  onBackPress?: () => void;
}

export default function Header({
  title,
  showBackButton = false,
  rightComponent,
  onBackPress
}: HeaderProps) {
  const navigation = useNavigation();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.header, { backgroundColor: getColor('primary'), paddingHorizontal: getSpacing('lg'), paddingVertical: getSpacing('lg') }]}>
      <StatusBar barStyle="light-content" backgroundColor={getColor('primary')} />

      {/* Left side - Title (matching mockup) */}
      <View style={styles.leftContainer}>
        <Text style={[styles.headerTitle, { color: getColor('white') }]}>
          {title}
        </Text>
      </View>

      {/* Right side - Action button or back button */}
      <View style={styles.rightContainer}>
        {showBackButton ? (
          <TouchableOpacity style={createHeaderButtonStyle()} onPress={handleBackPress}>
            <Text style={[styles.backButtonText, { color: getColor('white') }]}>←</Text>
          </TouchableOpacity>
        ) : (
          rightComponent || <View style={styles.spacer} />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + getSpacing('lg') : getSpacing('lg'),
    ...getShadow('md'),
  },
  leftContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  rightContainer: {
    alignItems: 'flex-end',
  },
  headerTitle: {
    ...getTypography('lg', 'bold'),
  },
  backButtonText: {
    ...getTypography('lg', 'bold'),
  },
  spacer: {
    width: 40,
  },
});
