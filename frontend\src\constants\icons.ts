import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';

// Icon mapping for navigation tabs
export const TAB_ICONS = {
  Home: { name: 'home', IconComponent: MaterialIcons, emoji: '🏠' },
  Events: { name: 'event', IconComponent: MaterialIcons, emoji: '📅' },
  Jobs: { name: 'work', IconComponent: MaterialIcons, emoji: '💼' },
  People: { name: 'people', IconComponent: MaterialIcons, emoji: '👥' },
  Chat: { name: 'chat', IconComponent: MaterialIcons, emoji: '💬' },
  Debug: { name: 'build', IconComponent: MaterialIcons, emoji: '⚙️' },
} as const;

// Icon mapping for feature cards
export const FEATURE_ICONS = {
  events: { name: 'calendar-today', IconComponent: MaterialIcons, emoji: '📅' },
  jobs: { name: 'work-outline', IconComponent: MaterialIcons, emoji: '💼' },
  people: { name: 'people-outline', IconComponent: MaterialIcons, emoji: '👥' },
  chat: { name: 'chat-bubble-outline', IconComponent: MaterialIcons, emoji: '💬' },
  market: { name: 'store', IconComponent: MaterialIcons, emoji: '🛒' },
  news: { name: 'article', IconComponent: MaterialIcons, emoji: '📰' },
} as const;

// Icon mapping for header actions
export const HEADER_ICONS = {
  profile: { name: 'person', IconComponent: MaterialIcons, emoji: '👤' },
  logout: { name: 'logout', IconComponent: MaterialIcons, emoji: '🚪' },
  back: { name: 'arrow-back', IconComponent: MaterialIcons, emoji: '←' },
  menu: { name: 'menu', IconComponent: MaterialIcons, emoji: '☰' },
} as const;

// Icon categories for IconPicker component
export const ICON_PICKER_CATEGORIES = {
  popular: {
    title: 'פופולרי',
    icons: [
      { name: 'event', IconComponent: MaterialIcons },
      { name: 'celebration', IconComponent: MaterialIcons },
      { name: 'home', IconComponent: MaterialIcons },
      { name: 'local-pizza', IconComponent: MaterialIcons },
      { name: 'sports-soccer', IconComponent: MaterialIcons },
      { name: 'music-note', IconComponent: MaterialIcons },
      { name: 'book', IconComponent: MaterialIcons },
      { name: 'work', IconComponent: MaterialIcons },
      { name: 'star', IconComponent: MaterialIcons },
      { name: 'gps-fixed', IconComponent: MaterialIcons },
      { name: 'emoji-events', IconComponent: MaterialIcons },
      { name: 'favorite', IconComponent: MaterialIcons },
    ]
  },
  events: {
    title: 'אירועים',
    icons: [
      { name: 'celebration', IconComponent: MaterialIcons },
      { name: 'party-mode', IconComponent: MaterialIcons },
      { name: 'cake', IconComponent: MaterialIcons },
      { name: 'theater-comedy', IconComponent: MaterialIcons },
      { name: 'palette', IconComponent: MaterialIcons },
      { name: 'music-note', IconComponent: MaterialIcons },
      { name: 'mic', IconComponent: MaterialIcons },
      { name: 'piano', IconComponent: MaterialCommunityIcons },
      { name: 'trumpet', IconComponent: MaterialCommunityIcons },
      { name: 'movie', IconComponent: MaterialIcons },
      { name: 'festival', IconComponent: MaterialCommunityIcons },
      { name: 'confetti', IconComponent: MaterialCommunityIcons },
    ]
  },
  community: {
    title: 'קהילה',
    icons: [
      { name: 'home-city', IconComponent: MaterialCommunityIcons },
      { name: 'home', IconComponent: MaterialIcons },
      { name: 'house', IconComponent: MaterialCommunityIcons },
      { name: 'business', IconComponent: MaterialIcons },
      { name: 'account-balance', IconComponent: MaterialIcons },
      { name: 'store', IconComponent: MaterialIcons },
      { name: 'shopping-mall', IconComponent: MaterialCommunityIcons },
      { name: 'church', IconComponent: MaterialCommunityIcons },
      { name: 'mosque', IconComponent: MaterialCommunityIcons },
      { name: 'school', IconComponent: MaterialIcons },
      { name: 'local-hospital', IconComponent: MaterialIcons },
      { name: 'account-balance', IconComponent: MaterialIcons },
    ]
  },
  activities: {
    title: 'פעילויות',
    icons: [
      { name: 'sports-soccer', IconComponent: MaterialIcons },
      { name: 'sports-basketball', IconComponent: MaterialIcons },
      { name: 'sports-football', IconComponent: MaterialIcons },
      { name: 'sports-baseball', IconComponent: MaterialIcons },
      { name: 'sports-tennis', IconComponent: MaterialIcons },
      { name: 'sports-volleyball', IconComponent: MaterialIcons },
      { name: 'sports-handball', IconComponent: MaterialIcons },
      { name: 'sports-badminton', IconComponent: MaterialCommunityIcons },
      { name: 'boxing-glove', IconComponent: MaterialCommunityIcons },
      { name: 'pool', IconComponent: MaterialIcons },
      { name: 'directions-bike', IconComponent: MaterialIcons },
      { name: 'directions-run', IconComponent: MaterialIcons },
    ]
  },
  food: {
    title: 'אוכל',
    icons: [
      { name: 'local-pizza', IconComponent: MaterialIcons },
      { name: 'hamburger', IconComponent: MaterialCommunityIcons },
      { name: 'french-fries', IconComponent: MaterialCommunityIcons },
      { name: 'hot-dog', IconComponent: MaterialCommunityIcons },
      { name: 'sandwich', IconComponent: MaterialCommunityIcons },
      { name: 'taco', IconComponent: MaterialCommunityIcons },
      { name: 'pasta', IconComponent: MaterialCommunityIcons },
      { name: 'noodles', IconComponent: MaterialCommunityIcons },
      { name: 'soup', IconComponent: MaterialCommunityIcons },
      { name: 'pot-steam', IconComponent: MaterialCommunityIcons },
      { name: 'cake', IconComponent: MaterialIcons },
      { name: 'cupcake', IconComponent: MaterialCommunityIcons },
    ]
  },
  nature: {
    title: 'טבע',
    icons: [
      { name: 'park', IconComponent: MaterialIcons },
      { name: 'forest', IconComponent: MaterialIcons },
      { name: 'palm-tree', IconComponent: MaterialCommunityIcons },
      { name: 'leaf', IconComponent: MaterialCommunityIcons },
      { name: 'four-leaf-clover', IconComponent: MaterialCommunityIcons },
      { name: 'flower', IconComponent: MaterialCommunityIcons },
      { name: 'sunflower', IconComponent: MaterialCommunityIcons },
      { name: 'rose', IconComponent: MaterialCommunityIcons },
      { name: 'tulip', IconComponent: MaterialCommunityIcons },
      { name: 'cherry-blossom', IconComponent: MaterialCommunityIcons },
      { name: 'daisy', IconComponent: MaterialCommunityIcons },
      { name: 'butterfly', IconComponent: MaterialCommunityIcons },
    ]
  },
  education: {
    title: 'חינוך',
    icons: [
      { name: 'book', IconComponent: MaterialIcons },
      { name: 'menu-book', IconComponent: MaterialIcons },
      { name: 'edit', IconComponent: MaterialIcons },
      { name: 'note', IconComponent: MaterialIcons },
      { name: 'school', IconComponent: MaterialIcons },
      { name: 'person', IconComponent: MaterialIcons },
      { name: 'calculate', IconComponent: MaterialIcons },
      { name: 'straighten', IconComponent: MaterialIcons },
      { name: 'science', IconComponent: MaterialIcons },
      { name: 'biotech', IconComponent: MaterialIcons },
      { name: 'quiz', IconComponent: MaterialIcons },
      { name: 'assignment', IconComponent: MaterialIcons },
    ]
  },
  symbols: {
    title: 'סמלים',
    icons: [
      { name: 'star', IconComponent: MaterialIcons },
      { name: 'star-outline', IconComponent: MaterialIcons },
      { name: 'auto-awesome', IconComponent: MaterialIcons },
      { name: 'flash-on', IconComponent: MaterialIcons },
      { name: 'whatshot', IconComponent: MaterialIcons },
      { name: 'water-drop', IconComponent: MaterialIcons },
      { name: 'bolt', IconComponent: MaterialIcons },
      { name: 'wb-sunny', IconComponent: MaterialIcons },
      { name: 'nightlight', IconComponent: MaterialIcons },
      { name: 'gps-fixed', IconComponent: MaterialIcons },
      { name: 'emoji-events', IconComponent: MaterialIcons },
      { name: 'diamond', IconComponent: MaterialCommunityIcons },
    ]
  }
};

// Helper function to get icon component
export const getIconComponent = (iconName: string, iconSet: string = 'MaterialIcons') => {
  switch (iconSet) {
    case 'MaterialCommunityIcons':
      return MaterialCommunityIcons;
    case 'FontAwesome':
      return FontAwesome;
    case 'Ionicons':
      return Ionicons;
    default:
      return MaterialIcons;
  }
};
